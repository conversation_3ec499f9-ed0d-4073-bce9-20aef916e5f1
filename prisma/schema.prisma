generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                        Int                 @id @default(autoincrement())
  email                     String              @unique
  name                      String
  password                  String?
  level                     String              @default("A1")
  points                    Int                 @default(0)
  streakDays                Int                 @default(0)
  joinedAt                  DateTime            @default(now())
  learningGoals             String[]            @default([])
  completedLessons          Int                 @default(0)
  lastActive                DateTime            @default(now())
  dailyGoal                 Int                 @default(15)
  notifications             <PERSON>olean             @default(true)
  theme                     String              @default("light")
  aiCorrectionEnabled       Boolean             @default(true)
  aiVocabSuggestionsEnabled Boolean             @default(true)
  preferredVoice            String?
  speechRecognitionEnabled  <PERSON>olean             @default(true)
  conversations             Conversation[]
  examResults               ExamResult[]
  lessonProgress            LessonProgress[]
  practiceSessions          PracticeSession[]
  templateUsage             UserTemplateUsage[]
  vocabulary                UserVocabulary[]
}

model Lesson {
  id          Int              @id @default(autoincrement())
  title       String
  description String
  level       String
  duration    Int
  topics      String[]
  progress    LessonProgress[]
  sections    LessonSection[]
}

model LessonSection {
  id        Int              @id @default(autoincrement())
  lessonId  Int
  title     String
  type      String
  content   String?
  audioUrl  String?
  videoUrl  String?
  order     Int
  exercises LessonExercise[]
  lesson    Lesson           @relation(fields: [lessonId], references: [id])
}

model LessonExercise {
  id            Int           @id @default(autoincrement())
  sectionId     Int
  type          String
  question      String
  options       String[]
  correctAnswer String
  explanation   String?
  section       LessonSection @relation(fields: [sectionId], references: [id])
}

model LessonProgress {
  id          Int       @id @default(autoincrement())
  lessonId    Int
  userId      Int
  completed   Boolean   @default(false)
  score       Int       @default(0)
  startedAt   DateTime?
  completedAt DateTime?
  answers     Json?
  lesson      Lesson    @relation(fields: [lessonId], references: [id])
  user        User      @relation(fields: [userId], references: [id])

  @@unique([userId, lessonId])
}

model Vocabulary {
  id             Int              @id @default(autoincrement())
  word           String           @unique
  translation    String
  example        String
  level          String
  category       String?
  pronunciation  String?
  usageContext   String[]         @default([])
  practiceItems  PracticeItem[]
  userVocabulary UserVocabulary[]
}

model UserVocabulary {
  id              Int        @id @default(autoincrement())
  userId          Int
  vocabularyId    Int
  learned         Boolean    @default(false)
  lastPracticed   DateTime?
  nextReviewDate  DateTime?
  repetitionStage Int        @default(0)
  user            User       @relation(fields: [userId], references: [id])
  vocabulary      Vocabulary @relation(fields: [vocabularyId], references: [id])

  @@unique([userId, vocabularyId])
}

model Conversation {
  id            String                @id @default(uuid())
  userId        Int
  title         String
  context       String
  startedAt     DateTime              @default(now())
  lastMessageAt DateTime              @default(now())
  templateId    Int?
  template      ConversationTemplate? @relation(fields: [templateId], references: [id])
  user          User                  @relation(fields: [userId], references: [id])
  messages      Message[]
}

model Message {
  id                  Int          @id @default(autoincrement())
  conversationId      String
  role                String
  content             String
  timestamp           DateTime     @default(now())
  audioUrl            String?
  corrections         Json?
  suggestedVocabulary Json?
  conversation        Conversation @relation(fields: [conversationId], references: [id])
}

model ConversationTemplate {
  id                Int                 @id @default(autoincrement())
  title             String
  description       String
  systemPrompt      String
  initialMessage    String
  topics            String[]
  level             String
  conversations     Conversation[]
  userTemplateUsage UserTemplateUsage[]
}

model UserTemplateUsage {
  id         Int                  @id @default(autoincrement())
  userId     Int
  templateId Int
  usedAt     DateTime             @default(now())
  template   ConversationTemplate @relation(fields: [templateId], references: [id])
  user       User                 @relation(fields: [userId], references: [id])
}

model ExamResult {
  id          Int      @id @default(autoincrement())
  userId      Int
  examId      String
  section     String
  level       String
  score       Int
  details     Json
  completedAt DateTime
  timeSpent   Int
  user        User     @relation(fields: [userId], references: [id])
}

model PracticeSession {
  id                 String                      @id @default(uuid())
  userId             Int
  type               String
  duration           Int
  createdAt          DateTime                    @default(now())
  aiGenerated        Boolean                     @default(false)
  difficulty         String?
  score              Int?
  practiceItems      PracticeItem[]
  user               User                        @relation(fields: [userId], references: [id])
  pronunciationItems PronunciationPracticeItem[]
}

model PracticeItem {
  id             Int             @id @default(autoincrement())
  sessionId      String
  vocabularyId   Int
  exerciseType   String
  isCorrect      Boolean
  userAnswer     String
  expectedAnswer String
  session        PracticeSession @relation(fields: [sessionId], references: [id])
  vocabulary     Vocabulary      @relation(fields: [vocabularyId], references: [id])
}

model PronunciationExercise {
  id                    Int                         @id @default(autoincrement())
  text                  String
  translation           String?
  difficulty            String
  category              String?
  expectedPronunciation String?
  practiceItems         PronunciationPracticeItem[]
}

model PronunciationPracticeItem {
  id              Int                   @id @default(autoincrement())
  sessionId       String
  exerciseId      Int
  userAudioUrl    String?
  transcript      String?
  similarityScore Float?
  feedback        Json?
  exercise        PronunciationExercise @relation(fields: [exerciseId], references: [id])
  session         PracticeSession       @relation(fields: [sessionId], references: [id])
}

model GrammarRule {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  examples    String[]
  level       String
  category    String
}
